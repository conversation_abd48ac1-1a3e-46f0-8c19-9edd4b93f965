#!/usr/bin/env python
"""
启动 OpenManus 并集成 Playwright MCP 源码
"""
import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger
from app.config import config


async def ensure_mcp_config():
    """确保 MCP 配置文件存在并包含 Playwright 配置"""
    mcp_config_path = project_root / "config" / "mcp.json"
    
    # 如果配置文件不存在，创建一个基本配置
    if not mcp_config_path.exists():
        logger.info(f"创建 MCP 配置文件: {mcp_config_path}")
        config_data = {
            "mcpServers": {
                "playwright": {
                    "type": "stdio",
                    "command": "npx",
                    "args": ["@playwright/mcp@latest"]
                }
            }
        }
        
        with open(mcp_config_path, "w") as f:
            json.dump(config_data, f, indent=2)
        
        return
    
    # 如果配置文件存在，确保包含 Playwright 配置
    try:
        with open(mcp_config_path, "r") as f:
            config_data = json.load(f)
        
        if "mcpServers" not in config_data:
            config_data["mcpServers"] = {}
        
        # 检查是否已有 Playwright 配置
        if "playwright" not in config_data["mcpServers"]:
            logger.info("添加 Playwright MCP 配置")
            config_data["mcpServers"]["playwright"] = {
                "type": "stdio",
                "command": "npx",
                "args": ["@playwright/mcp@latest"]
            }
            
            with open(mcp_config_path, "w") as f:
                json.dump(config_data, f, indent=2)
    
    except Exception as e:
        logger.error(f"处理 MCP 配置文件时出错: {str(e)}", exc_info=True)


async def main():
    """启动 OpenManus 并集成 Playwright MCP 源码"""
    # 确保 MCP 配置正确
    await ensure_mcp_config()
    
    try:
        # 创建 Manus 实例
        agent = await Manus.create()
        
        # 初始化 MCP 服务器连接
        await agent.initialize_mcp_servers()
        
        print("\nOpenManus 与 Playwright MCP 源码集成启动成功！")
        print("输入 'exit' 退出")
        
        while True:
            prompt = input("\n请输入您的指令: ")
            if prompt.lower() in ["exit", "quit", "q"]:
                break
                
            if not prompt.strip():
                continue
                
            logger.info("正在处理您的请求...")
            response = await agent.run(prompt)
            logger.info("请求处理完成")
            
    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"运行时出错: {str(e)}", exc_info=True)
    finally:
        # 确保资源被清理
        if 'agent' in locals():
            await agent.cleanup()
            
        logger.info("会话已结束")


if __name__ == "__main__":
    asyncio.run(main())