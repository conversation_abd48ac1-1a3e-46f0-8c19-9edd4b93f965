"""
Playwright 工具实现
"""
import os
import json
import asyncio
import uuid
from typing import Dict, Any, Optional
from pathlib import Path

from app.logger import logger
from app.config import config


class PlaywrightTool:
    """Playwright 工具实现，通过 MCP 客户端与 Playwright MCP 服务通信"""
    
    def __init__(self):
        self.mcp_client = None
        self.browsers: Dict[str, Any] = {}
        self.pages: Dict[str, Any] = {}
        self.screenshots_dir = Path(config.workspace_root) / "screenshots"
        
    async def initialize(self) -> None:
        """初始化 Playwright 工具"""
        from app.agent.mcp_client import MCPClient
        
        # 创建截图目录
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # 连接到 Playwright MCP 服务
        self.mcp_client = MCPClient("playwright")
        try:
            await self.mcp_client.connect()
            logger.info("已连接到 Playwright MCP 服务")
        except Exception as e:
            logger.error(f"连接到 Playwright MCP 服务时出错: {str(e)}", exc_info=True)
            raise
    
    async def run(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行 Playwright 操作
        
        Args:
            params: 操作参数
                action: 操作名称
                [其他参数取决于操作]
                
        Returns:
            Dict[str, Any]: 操作结果
        """
        if not self.mcp_client:
            return {"error": "Playwright MCP 客户端未初始化"}
            
        action = params.get("action", "")
        
        try:
            if action == "launch":
                return await self._launch_browser(params)
            elif action == "newPage":
                return await self._new_page(params)
            elif action == "goto":
                return await self._goto(params)
            elif action == "fill":
                return await self._fill(params)
            elif action == "click":
                return await self._click(params)
            elif action == "screenshot":
                return await self._screenshot(params)
            elif action == "evaluate":
                return await self._evaluate(params)
            elif action == "content":
                return await self._content(params)
            elif action == "closePage":
                return await self._close_page(params)
            elif action == "closeBrowser":
                return await self._close_browser(params)
            else:
                return {"error": f"未知操作: {action}"}
        except Exception as e:
            logger.error(f"执行 Playwright 操作 {action} 时出错: {str(e)}", exc_info=True)
            return {"error": str(e)}
    
    async def _launch_browser(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """启动浏览器"""
        browser_type = params.get("browserType", "chromium")
        headless = params.get("headless", False)
        
        result = await self.mcp_client.call_tool("browser_launch", {
            "browserType": browser_type,
            "headless": headless
        })
        
        browser_id = str(uuid.uuid4())
        self.browsers[browser_id] = result
        
        return {"browserId": browser_id}
    
    async def _new_page(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """创建新页面"""
        browser_id = params.get("browserId")
        if not browser_id or browser_id not in self.browsers:
            return {"error": "无效的浏览器 ID"}
        
        result = await self.mcp_client.call_tool("browser_newPage", {})
        
        page_id = str(uuid.uuid4())
        self.pages[page_id] = result
        
        return {"pageId": page_id}
    
    async def _goto(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """导航到 URL"""
        page_id = params.get("pageId")
        url = params.get("url")
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        await self.mcp_client.call_tool("browser_navigate", {
            "url": url
        })
        
        return {"success": True}
    
    async def _fill(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """填充表单字段"""
        page_id = params.get("pageId")
        selector = params.get("selector")
        value = params.get("value")
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        await self.mcp_client.call_tool("browser_fill", {
            "selector": selector,
            "text": value
        })
        
        return {"success": True}
    
    async def _click(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """点击元素"""
        page_id = params.get("pageId")
        selector = params.get("selector")
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        await self.mcp_client.call_tool("browser_click", {
            "selector": selector
        })
        
        return {"success": True}
    
    async def _screenshot(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """截图"""
        page_id = params.get("pageId")
        path = params.get("path")
        full_page = params.get("fullPage", False)
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        if not path:
            path = f"screenshot_{uuid.uuid4()}.png"
            
        # 确保路径是相对于截图目录的
        if not os.path.isabs(path):
            path = str(self.screenshots_dir / path)
        
        result = await self.mcp_client.call_tool("browser_screenshot", {
            "path": path,
            "fullPage": full_page
        })
        
        return {"path": path}
    
    async def _evaluate(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行 JavaScript"""
        page_id = params.get("pageId")
        expression = params.get("expression")
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        result = await self.mcp_client.call_tool("browser_evaluate", {
            "expression": expression
        })
        
        return {"result": result}
    
    async def _content(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取页面内容"""
        page_id = params.get("pageId")
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        result = await self.mcp_client.call_tool("browser_content", {})
        
        return {"content": result}
    
    async def _close_page(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """关闭页面"""
        page_id = params.get("pageId")
        
        if not page_id or page_id not in self.pages:
            return {"error": "无效的页面 ID"}
        
        await self.mcp_client.call_tool("browser_closePage", {})
        
        del self.pages[page_id]
        
        return {"success": True}
    
    async def _close_browser(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """关闭浏览器"""
        browser_id = params.get("browserId")
        
        if not browser_id or browser_id not in self.browsers:
            return {"error": "无效的浏览器 ID"}
        
        await self.mcp_client.call_tool("browser_closeBrowser", {})
        
        del self.browsers[browser_id]
        
        # 清理相关的页面
        page_ids_to_remove = []
        for page_id in self.pages:
            if self.pages[page_id].get("browserId") == browser_id:
                page_ids_to_remove.append(page_id)
                
        for page_id in page_ids_to_remove:
            del self.pages[page_id]
        
        return {"success": True}