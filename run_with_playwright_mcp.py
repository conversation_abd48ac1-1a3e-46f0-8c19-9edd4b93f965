#!/usr/bin/env python
"""
启动 OpenManus 并集成 Playwright MCP 源码
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger


async def main():
    """启动 OpenManus 并集成 Playwright MCP 源码"""
    try:
        # 创建 Manus 实例
        agent = await Manus.create()
        
        # 手动连接到 Playwright MCP 服务
        command = "node"
        args = ["external/playwright-mcp/cli.js"]
        server_id = "playwright"
        
        logger.info(f"正在连接到 Playwright MCP 服务...")
        
        await agent.connect_mcp_server(
            command,
            server_id,
            use_stdio=True,
            stdio_args=args
        )
        
        # 检查连接状态
        if agent.mcp_clients.sessions:
            available_tools = list(agent.mcp_clients.tool_map.keys())
            playwright_tools = [tool for tool in available_tools if 'playwright' in tool.lower()]
            
            logger.info(f"✅ 成功连接到 Playwright MCP 服务！")
            logger.info(f"📋 发现 {len(playwright_tools)} 个 Playwright 工具")
            
            print("\n🎉 OpenManus 与 Playwright MCP 源码集成启动成功！")
            print("📝 可用的 Playwright 工具包括:")
            print("   - browser_navigate: 导航到网页")
            print("   - browser_take_screenshot: 截图")
            print("   - browser_click: 点击元素")
            print("   - browser_type: 输入文本")
            print("   - 以及更多...")
            print("\n💡 示例使用:")
            print("   '请使用 Playwright 打开 https://www.example.com 并截图'")
            print("   '请使用 Playwright 在百度搜索 OpenManus'")
            print("\n输入 'exit' 退出")
            
            while True:
                try:
                    prompt = input("\n🤖 请输入您的指令: ")
                    if prompt.lower() in ["exit", "quit", "q"]:
                        break
                        
                    if not prompt.strip():
                        continue
                        
                    logger.info("正在处理您的请求...")
                    response = await agent.run(prompt)
                    logger.info("请求处理完成")
                    
                except KeyboardInterrupt:
                    break
        else:
            logger.error("❌ 未能连接到 Playwright MCP 服务")
            
    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"运行时出错: {str(e)}", exc_info=True)
    finally:
        # 确保资源被清理
        if 'agent' in locals():
            await agent.cleanup()
            
        logger.info("会话已结束")


if __name__ == "__main__":
    asyncio.run(main())
