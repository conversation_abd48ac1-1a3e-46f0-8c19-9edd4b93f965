"""
Playwright 浏览器工具
"""
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from app.tool.base import BaseTool
from app.tool.playwright_tool import PlaywrightTool


class PlaywrightBrowser(BaseTool):
    """Playwright 浏览器工具"""
    
    name: str = "playwright_browser"
    description: str = "使用 Playwright 进行浏览器自动化操作"
    parameters: dict = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "enum": [
                    "launch", "newPage", "goto", "fill", "click", 
                    "screenshot", "evaluate", "content", "closePage", 
                    "closeBrowser"
                ],
                "description": "要执行的浏览器操作",
            },
            "browserType": {
                "type": "string",
                "enum": ["chromium", "firefox", "webkit"],
                "description": "浏览器类型（仅用于 launch 操作）",
            },
            "headless": {
                "type": "boolean",
                "description": "是否以无头模式运行浏览器（仅用于 launch 操作）",
            },
            "browserId": {
                "type": "string",
                "description": "浏览器 ID",
            },
            "pageId": {
                "type": "string",
                "description": "页面 ID",
            },
            "url": {
                "type": "string",
                "description": "要导航到的 URL（用于 goto 操作）",
            },
            "selector": {
                "type": "string",
                "description": "CSS 选择器（用于 fill、click 等操作）",
            },
            "value": {
                "type": "string",
                "description": "要填入的值（用于 fill 操作）",
            },
            "path": {
                "type": "string",
                "description": "截图保存路径（用于 screenshot 操作）",
            },
            "fullPage": {
                "type": "boolean",
                "description": "是否截取整个页面（用于 screenshot 操作）",
            },
            "expression": {
                "type": "string",
                "description": "要执行的 JavaScript 表达式（用于 evaluate 操作）",
            },
        },
        "required": ["action"],
        "allOf": [
            {
                "if": {
                    "properties": {"action": {"enum": ["launch"]}}
                },
                "then": {
                    "properties": {"browserType": {"default": "chromium"}}
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["newPage", "closeBrowser"]}}
                },
                "then": {
                    "required": ["browserId"]
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["goto"]}}
                },
                "then": {
                    "required": ["pageId", "url"]
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["fill"]}}
                },
                "then": {
                    "required": ["pageId", "selector", "value"]
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["click"]}}
                },
                "then": {
                    "required": ["pageId", "selector"]
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["screenshot"]}}
                },
                "then": {
                    "required": ["pageId"]
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["evaluate"]}}
                },
                "then": {
                    "required": ["pageId", "expression"]
                }
            },
            {
                "if": {
                    "properties": {"action": {"enum": ["content", "closePage"]}}
                },
                "then": {
                    "required": ["pageId"]
                }
            }
        ]
    }
    
    _playwright_tool: Optional[PlaywrightTool] = None
    
    async def execute(self, **kwargs) -> str:
        """
        执行 Playwright 浏览器操作
        
        Args:
            **kwargs: 操作参数
                action: 操作名称
                [其他参数取决于操作]
                
        Returns:
            str: 操作结果
        """
        # 初始化 PlaywrightTool
        if self._playwright_tool is None:
            self._playwright_tool = PlaywrightTool()
            await self._playwright_tool.initialize()
        
        # 执行操作
        result = await self._playwright_tool.run(kwargs)
        
        # 格式化结果
        if "error" in result:
            return f"错误: {result['error']}"
        
        action = kwargs.get("action", "")
        
        if action == "launch":
            return f"成功启动浏览器，ID: {result.get('browserId')}"
        elif action == "newPage":
            return f"成功创建新页面，ID: {result.get('pageId')}"
        elif action == "goto":
            return f"成功导航到 {kwargs.get('url')}"
        elif action == "fill":
            return f"成功在 {kwargs.get('selector')} 中填入值"
        elif action == "click":
            return f"成功点击 {kwargs.get('selector')}"
        elif action == "screenshot":
            return f"成功截图，保存到 {result.get('path')}"
        elif action == "evaluate":
            return f"JavaScript 执行结果: {result.get('result')}"
        elif action == "content":
            content = result.get('content', '')
            if len(content) > 500:
                content = content[:500] + "... (内容已截断)"
            return f"页面内容: {content}"
        elif action == "closePage":
            return "成功关闭页面"
        elif action == "closeBrowser":
            return "成功关闭浏览器"
        else:
            return f"未知操作: {action}"