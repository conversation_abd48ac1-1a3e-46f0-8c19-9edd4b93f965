"""
Playwright MCP 连接器
"""
import os
import sys
import asyncio
import subprocess
from pathlib import Path
from typing import Optional, List

from app.logger import logger
from app.config import config


class PlaywrightMCPConnector:
    """Playwright MCP 连接器，用于管理与 Playwright MCP 的连接"""
    
    def __init__(self):
        self.project_root = Path(config.root_path)
        self.playwright_mcp_dir = self.project_root / "external" / "playwright-mcp"
        self.process: Optional[subprocess.Popen] = None
        self.port = 8931  # 默认端口
        
    async def connect(self) -> bool:
        """
        连接到 Playwright MCP 服务
        
        Returns:
            bool: 连接是否成功
        """
        # 检查 Playwright MCP 目录是否存在
        if not self.playwright_mcp_dir.exists():
            logger.error(f"Playwright MCP 目录不存在: {self.playwright_mcp_dir}")
            logger.info("请先运行 'python scripts/setup_playwright_mcp.py' 安装 Playwright MCP")
            return False
            
        # 检查是否已经有进程在运行
        if self.process is not None and self.process.poll() is None:
            logger.info("Playwright MCP 服务已经在运行")
            return True
            
        # 启动 Playwright MCP 服务
        try:
            # 使用 npx 启动 Playwright MCP
            cmd = [
                "npx", 
                "--yes", 
                "--", 
                "node", 
                str(self.playwright_mcp_dir / "cli.js"),
                "--port", 
                str(self.port)
            ]
            
            logger.info(f"启动 Playwright MCP 服务: {' '.join(cmd)}")
            
            # 创建环境变量
            env = os.environ.copy()
            
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                cwd=str(self.playwright_mcp_dir),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            # 等待服务启动
            logger.info("等待 Playwright MCP 服务启动...")
            await asyncio.sleep(2)
            
            # 检查进程是否仍在运行
            if self.process.poll() is not None:
                stderr = self.process.stderr.read() if self.process.stderr else ""
                logger.error(f"Playwright MCP 服务启动失败: {stderr}")
                return False
                
            # 更新 mcp.json 配置
            await self._update_mcp_config()
            
            logger.info(f"Playwright MCP 服务已启动，端口: {self.port}")
            return True
            
        except Exception as e:
            logger.error(f"启动 Playwright MCP 服务时出错: {str(e)}", exc_info=True)
            return False
    
    async def _update_mcp_config(self) -> None:
        """更新 mcp.json 配置"""
        import json
        
        mcp_config_path = self.project_root / "config" / "mcp.json"
        
        try:
            # 读取现有配置
            if mcp_config_path.exists():
                with open(mcp_config_path, "r") as f:
                    config_data = json.load(f)
            else:
                config_data = {"mcpServers": {}}
                
            # 更新 playwright 配置
            config_data["mcpServers"]["playwright"] = {
                "type": "sse",
                "url": f"http://localhost:{self.port}/sse"
            }
            
            # 写入配置
            with open(mcp_config_path, "w") as f:
                json.dump(config_data, f, indent=2)
                
            logger.info(f"已更新 MCP 配置: {mcp_config_path}")
            
        except Exception as e:
            logger.error(f"更新 MCP 配置时出错: {str(e)}", exc_info=True)
    
    async def disconnect(self) -> None:
        """断开与 Playwright MCP 服务的连接"""
        if self.process is not None and self.process.poll() is None:
            logger.info("关闭 Playwright MCP 服务...")
            
            try:
                # 尝试正常终止进程
                self.process.terminate()
                
                # 等待进程终止
                try:
                    await asyncio.wait_for(
                        asyncio.create_subprocess_shell(
                            f"wait {self.process.pid}",
                            stdout=asyncio.subprocess.DEVNULL,
                            stderr=asyncio.subprocess.DEVNULL
                        ),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    # 如果超时，强制终止
                    logger.warning("Playwright MCP 服务未能正常终止，强制终止")
                    self.process.kill()
                
                logger.info("Playwright MCP 服务已关闭")
                
            except Exception as e:
                logger.error(f"关闭 Playwright MCP 服务时出错: {str(e)}", exc_info=True)
            
            finally:
                self.process = None