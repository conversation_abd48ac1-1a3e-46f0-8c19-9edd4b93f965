"""
Playwright MCP 源码启动器
"""
import os
import sys
import subprocess
import asyncio
from pathlib import Path
from typing import List, Optional, Dict, Any

from app.logger import logger


class PlaywrightMCPLauncher:
    """Playwright MCP 源码启动器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.server_ready = asyncio.Event()
        
        # 获取 Playwright MCP 源码路径
        self.mcp_path = Path(__file__).resolve().parent.parent.parent / "external" / "playwright-mcp"
        if not self.mcp_path.exists():
            raise FileNotFoundError(f"Playwright MCP 源码目录不存在: {self.mcp_path}")
    
    async def start(self, args: List[str] = None, env: Dict[str, str] = None) -> bool:
        """
        启动 Playwright MCP 服务
        
        Args:
            args: 传递给 Playwright MCP 的额外参数
            env: 环境变量
            
        Returns:
            bool: 服务是否成功启动
        """
        if self.process and self.process.poll() is None:
            logger.info("Playwright MCP 服务已经在运行")
            return True
            
        # 构建命令
        cmd = ["node", str(self.mcp_path / "dist" / "cli.js")]
        if args:
            cmd.extend(args)
            
        # 构建环境变量
        env_vars = os.environ.copy()
        if env:
            env_vars.update(env)
            
        try:
            # 检查 Playwright MCP 源码是否已编译
            dist_path = self.mcp_path / "dist"
            cli_path = dist_path / "cli.js"
            
            if not dist_path.exists() or not cli_path.exists():
                logger.info("Playwright MCP 源码尚未编译，正在编译...")
                
                # 编译 Playwright MCP 源码
                compile_process = subprocess.run(
                    ["npm", "run", "build"],
                    cwd=str(self.mcp_path),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                
                if compile_process.returncode != 0:
                    logger.error(f"编译 Playwright MCP 源码失败: {compile_process.stderr}")
                    return False
                
                logger.info("Playwright MCP 源码编译成功")
                
            # 启动 Playwright MCP 服务
            logger.info(f"正在启动 Playwright MCP 服务: {' '.join(cmd)}")
            
            # 使用 subprocess.Popen 启动服务
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                cwd=str(self.mcp_path),
                env=env_vars
            )
            
            # 创建任务来监控输出
            asyncio.create_task(self._monitor_output())
            
            # 等待服务准备就绪
            try:
                await asyncio.wait_for(self.server_ready.wait(), timeout=10)
                logger.info("Playwright MCP 服务已准备就绪")
                return True
            except asyncio.TimeoutError:
                logger.warning("等待 Playwright MCP 服务准备就绪超时")
                return False
                
        except FileNotFoundError:
            logger.error("找不到 node 命令。请确保已安装 Node.js")
            return False
        except Exception as e:
            logger.error(f"启动 Playwright MCP 服务时出错: {str(e)}")
            return False
    
    async def _monitor_output(self) -> None:
        """监控服务输出"""
        if not self.process:
            return
            
        while self.process.poll() is None:
            line = self.process.stdout.readline()
            if not line:
                break
                
            line = line.strip()
            if line:
                logger.debug(f"Playwright MCP: {line}")
                
                # 检测服务是否已准备就绪
                if "MCP server started" in line or "Server listening" in line:
                    self.server_ready.set()
        
        # 读取任何剩余的错误输出
        if self.process.stderr:
            for line in self.process.stderr:
                if line.strip():
                    logger.error(f"Playwright MCP 错误: {line.strip()}")
    
    async def stop(self) -> None:
        """停止 Playwright MCP 服务"""
        if self.process and self.process.poll() is None:
            logger.info("正在停止 Playwright MCP 服务")
            try:
                self.process.terminate()
                try:
                    await asyncio.wait_for(
                        asyncio.create_subprocess_exec(
                            *[sys.executable, "-c", "import asyncio; asyncio.run(asyncio.sleep(0.1))"]
                        ),
                        timeout=5
                    )
                except asyncio.TimeoutError:
                    logger.warning("Playwright MCP 服务未能及时终止，强制结束进程")
                    self.process.kill()
            except Exception as e:
                logger.error(f"停止 Playwright MCP 服务时出错: {str(e)}")
            finally:
                self.process = None
                self.server_ready.clear()